export default {
  '94': 'Log out',
  '95': 'Confirm',
  '96': 'Previous Page',
  '97': 'Next Page',
  '98': 'Jump to Page',
  '99': '<PERSON>',
  '100': 'Jump',
  '102': 'Action',
  '106': 'Training Parameters',
  '108': 'Name',
  '109': 'Description',
  '113': 'Back',
  '114': 'Add User',
  '115': 'Model Name',
  '179': 'Delete',
  '180': 'Confirm deletion',
  '181': 'Are you sure you want to delete? This action cannot be undone.',
  '182': 'Cancel',
  '183': 'Delete this user?',
  '184':
    'This operation will permanently delete the user. Please proceed with caution.',
  '259': 'Status',
  '260': 'Training Type',
  '261': 'Training Name',
  '262': 'Fine-tuning Task History',
  '263': 'New Training Task',
  '264': 'Training Log Comparison',
  '265': 'Load to Third Party',
  '266': 'Architecture',
  '267': 'Tags',
  '268': 'Training Framework',
  '269': 'Disk Space Occupancy',
  '270': 'Edit',
  '271': 'Edit Model',
  '272': 'Add Model',
  '273': 'Template',
  '274': 'Saved Model List',
  '275': 'Base Model',
  '276': 'Deploy',
  '277': 'Log',
  '278': 'Provider',
  '279': 'Model Path',
  '280': 'Fine-tuning Dataset',
  '281': 'Save',
  '283': 'Deployment Environment',
  '284': 'Add Environment',
  '285': 'Environment Name',
  '286': 'Environment Address',
  '287': 'Tip',
  '288':
    'The current version does not support the addition of method functionality. To unlock this feature and access more professional services, please contact the team to upgrade to the Professional Edition.',
  '289': 'Training Method',
  '290': 'Training File',
  '291': 'Version',
  '292': 'Model Type',
  '293': 'Language',
  '294': 'Task',
  '295': 'License',
  '296': 'Metrics',
  '297': 'Saved Format',
  '298': 'File Path',
  '299': 'Deployment Framework',
  '300': 'API Address',
  '301': 'Latency',
  '302': 'Limitations',
  '303': 'Author',
  '304': 'Created At',
  '305': 'Pre-training Dataset',
  '306': 'Training Objective',
  '307': 'Save Format',
  '308': 'Fine-tuning Objective',
  '309': 'Training Hyperparameters',
  '310': 'View Details',
  '311': 'Model Deploying Logs',
  '323': 'Delete successful',
  '324': 'Delete failed',
  '325': 'Add successful',
  '326': 'Add failed:',
  '327': 'User management',
  '328': 'User',
  '329': 'Self',
  '331': 'Phone Number',
  '332': 'Username',
  '333': 'Please enter username',
  '334': 'Nickname',
  '335': 'Please enter nickname',
  '336': 'Password',
  '337': 'Please enter password',
  '338': 'Phone number',
  '339': 'Email',
  '340': 'Add',
  '364': 'Evaluation Type',
  '365': 'Model A',
  '366': 'Model B',
  '367': 'Referee Model',
  '369': 'Status',
  '370': 'Start Time',
  '371': 'End Time',
  '892': 'Unknown error',
  '893': 'No system built-in default plan',
  '894': 'Login successful, please continue',
  '895': 'Your login has expired, please log in again',
  '896': 'Company code',
  '897': 'Are you sure you want to exit to the login page?',
  '898': 'Log out',
  '899': 'Log in again}',
  '900': 'Offline notification',
  '901':
    'This account has been logged in by another user, you have been logged off, please log in again.',
  '902': 'Completed',
  '907': 'Original template',
  '908': 'System template',
  '909': 'Other-created template',
  '910': 'Self-created template',
  '911': 'Failed to get the user list',
  '912': 'Failed to get the user list:',
  '913': 'Task ID',
  '914': 'Created By',
  '915': 'Template Type',
  '916': 'In progress',
  '917': 'Failed',
  '918': 'Invalid response format',
  '919': 'Request failed',
  '920': 'Failed to retrieve log data:',
  '921': 'Failed to retrieve log data',
  '922': 'Please select at least two records for comparison',
  '923': 'A maximum of two records can be compared',
  '924': 'Date Range',
  '925': 'Status',
  '926': 'Please select a status',
  '927': 'Please enter the task name',
  '928': 'Template Type',
  '929': 'Please select a type',
  '930': 'Please select a user',
  '931': 'Search',
  '932': 'Reset',
  '933': 'Failed to get the dataset list:',
  '934': 'Successfully deleted the dataset',
  '935': 'Failed to delete the dataset:',
  '936': 'Failed to get the list',
  '937': 'View',
  '938': 'Are you sure you want to delete this dataset?',
  '939':
    'This operation will permanently delete the dataset, please proceed with caution.',
  '940': 'Dataset List',
  '941': 'Upload New Dataset',
  '942': 'This operation is irreversible, please proceed with caution.',
  '943': 'Knowledge Base Name',
  '944': 'Knowledge Base Description',
  '945': 'Embedding Model',
  '946': 'File Number',
  '947': 'Vector Model',
  '948': 'Update time',
  '949': 'Are you sure you want to delete this knowledge base?',
  '950':
    'This operation will permanently delete the knowledge base, please proceed with caution.',
  '951': 'New Knowledge Base',
  '952': 'Knowledge Base List',
  '953': 'Who are you?',
  '954': 'Explain the Pythagorean theorem',
  '955': 'Please select a model',
  '958': 'Knowledge Base',
  '961': 'Clear Conversation',
  '967': 'Failed to obtain the plan template',
  '968': 'Error:',
  '969': 'Run AI task',
  '970': 'Connector',
  '971': 'Data',
  '972': 'Training resources',
  '973': 'Task details',
  '974': 'Save new model',
  '975': 'Copy task',
  '976': 'Train new version',
  '978': 'Server returned an error',
  '979': 'Download failed:',
  '980': 'Download failed',
  '981': 'Model A',
  '982': 'Model B',
  '983': 'Referee Model',
  '984': 'Dataset',
  '985': 'Download Result',
  '986': 'Model Evaluation',
  '987': 'New Evaluation Task',
  '988': 'Please enter the correct personal information code',
  '989': 'Please enter your personal information code:',
  '990': 'Turn off AI assistant',
  '991': 'Turn on AI assistant',
  '992': 'Modification successful',
  '993': 'Change password',
  '994': 'Please enter a new password',
  '995': 'Please enter the new password again',
  '996': 'Submit',
  '997': 'Upload failed',
  '998': 'Error:',
  '999': 'Settings',
  '1000': 'Avatar:',
  '1001': 'Upload',
  '1002': 'Name:',
  '1003': 'Email:',
  '1004': 'Company:',
  '1005': 'Plan scope:',
  '1006': 'AI assistant settings',
  '1007': 'Copy succeeded',
  '1008': 'Copy failed',
  '1009': 'An error occurred on the server, please try again later',
  '1015': 'Dataset Name',
  '1016': 'Please enter the dataset name',
  '1017': 'Dataset Description',
  '1018': 'Please enter the dataset description',
  '1019': 'Upload File',
  '1020': 'Please select the file to upload',
  '1022': 'Task Overview',
  '1023': 'Task Evaluation',
  '1024': 'Task Log',
  '1025': 'Return to Log',
  '1026': 'Fine-tuning Log Details',
  '1027': 'Creation Successful',
  '1028': 'Creation failed, please try again',
  '1029': 'Creation Failed',
  '1030': 'Please enter the knowledge base name',
  '1031': 'Please enter the knowledge base description',
  '1032': 'Please select the vector model',
  '1034': 'File parsing failed:',
  '1035': 'Failed to get file list',
  '1036': 'Not parsed',
  '1037': 'Parsed successfully',
  '1038': 'Parsing failed',
  '1039': 'Parsing in progress',
  '1040': 'File Name',
  '1041': 'File Size',
  '1042': 'File Format',
  '1043': 'Import Time',
  '1044': 'Download',
  '1045': 'Search files',
  '1046': 'Import files',
  '1047': 'Knowledge base details',
  '1048': 'Upload successful',
  '1049': 'Upload failed, please try again',
  '1050': 'Select file',
  '1051': 'Click or drag files to this area to upload',
  '1052':
    'Only single file uploads are supported, file formats: PDF, DOCX, TXT, XLSX, CSV',
  '1053': 'Parse on creation',
  '1055': 'Quantization Method',
  '1056': 'Service Address',
  '1057': 'Deployed Model List',
  '1058': 'Model Alias',
  '1059': 'Base Model List',
  '1060': 'Please enter the model alias',
  '1061': 'Base Model Name',
  '1062': 'Please enter the base model name',
  '1063': 'Please enter the model path',
  '1064': 'Confirm',
  '1065': 'Column settings',
  '1066': 'Model deleted successfully',
  '1067': 'Model deletion failed',
  '1068': 'Model edited successfully',
  '1069': 'Model editing failed:',
  '1070': 'Model added successfully',
  '1071': 'Model addition failed:',
  '1072': 'Operation failed',
  '1073': 'Test failed',
  '1075': 'Third-Party Model List',
  '1077': 'Fetching model configuration:',
  '1078': 'Failed to fetch model configuration:',
  '1079': 'Parameter Details',
  '1080': 'Log Content',
  '1081': 'Result',
  '1082': 'Model Evaluation Log',
  '1083': 'Model 1',
  '1084': 'Model 1 - Model name',
  '1085': 'Model 2',
  '1086': 'Model 2 - Model Name',
  '1087': 'Evaluation Model',
  '1088': 'Select the evaluation mode you want to use',
  '1089': 'Referee Mode',
  '1090': 'Evaluate using Model A, Model B, and Referee Model',
  '1091': 'Comparison Mode',
  '1092': 'Compare using only Model A and Model B',
  '1093': 'Evaluation Mode',
  '1094': "Evaluate Model A's performance using the Referee Model",
  '1095': 'Single Model Mode',
  '1096': 'Output using only Model A',
  '1097': 'Dataset Selection',
  '1098': 'Select the base dataset for evaluation',
  '1099': 'Please select a dataset',
  '1100': 'Dataset Size',
  '1101': 'Configure the size of the evaluation dataset',
  '1102': 'Select All',
  '1103': 'Preview Settings',
  '1104':
    'Set the preview count and view partial results. Preview results are displayed below this page. Scroll down to view.',
  '1105': 'Preview Quantity:',
  '1106': 'Preview Results',
  '1107': 'Submit Evaluation',
  '1108': 'Submit All Data for Full Evaluation',
  '1109': 'Failed to Retrieve Dataset Details:',
  '1110': 'Serial Number',
  '1111': 'Successfully Retrieved Dataset Logs:',
  '1112': 'Failed to Retrieve Dataset Logs:',
  '1113': 'No Matching Log Records',
  '1114': 'Please select a log',
  '1115': 'Value',
  '1116': 'Steps',
  '1117': 'Failed to fetch overview log:',
  '1118': 'Failed to fetch training progress:',
  '1119': 'Log Overview',
  '1120': 'Full Log Content',
  '1121': 'Update Successful',
  '1122': 'Please Enter the Environment Address',
  '1123': 'Add Method',
  '1124': 'Connection Test Successful',
  '1125': 'Connection Test Failed',
  '1126': 'Privatization',
  '1127': 'Request Address',
  '1128': 'Test Connection',
  '1129': 'Not Tested',
  '1130': 'Test Passed',
  '1131': 'Testing...',
  '1132': 'Please Select a Provider',
  '1133': 'Please Select a Model Type',
  '1134': 'Please Fill in the Model',
  '1135': 'Please Enter the API Address',
  '1136': 'API Key',
  '1137': 'If You Need to Modify, Please Enter a New Key',
  '1138': 'Please enter the API key',
  '1139': 'Name:',
  '1140': 'Knowledge Base:',
  '1141': 'None',
  '1142': 'Evaluation Mode:',
  '1143': 'Dataset Information:',
  '1144': 'Total Evaluation Count:',
  '1145': 'All',
  '1146': 'Copy Parameters',
  '1147': 'Model Prompt',
  '1148': 'System Prompt',
  '1149': 'User Prompt',
  '1150': 'Referee Model Prompt',
  '1151': 'Referee System Prompt',
  '1152': 'Referee User Prompt',
  '1163': 'Model Selection',
  '1164': 'Select the model and knowledge base to evaluate',
  '1165': 'Model A',
  '1166': 'Please select Model A',
  '1167': 'Select Model A',
  '1168': 'Model A Knowledge Base',
  '1169': 'No knowledge base',
  '1170': 'Model B',
  '1171': 'Please select Model B',
  '1172': 'Select Model B',
  '1173': 'Model B Knowledge Base',
  '1174': 'Please select the referee model',
  '1175': 'Select the referee model',
  '1176': 'Referee Model Knowledge Base',
  '1177': 'Configure the prompts for Model A and Model B',
  '1180': 'The system prompt is used to set the behavior and role of the model',
  '1181':
    'The user prompt is the actual question or task you want the model to answer',
  '1183': 'Set the behavior and evaluation criteria for the referee model',
  '1185': "Guide the referee model on how to evaluate the model's responses",
  '1188': 'Evaluation Result:',
  '1189': 'Save model path',
  '1190': 'Deploy model name',
  '1191': 'Server address',
  '1192': 'Quantization',
  '1193': 'Save Path',
  '1194': 'Base Model Path',
  '1195': 'LoRA Adapter Path',
  '1196': 'Save Model Name',
  '1197': 'Search comparison...',
  '1198': 'Classification',
  '1199': 'Length',
  '1200': 'All lengths',
  '1201': 'Short text',
  '1202': 'Medium text',
  '1203': 'Long text',
  '1204': 'Sort',
  '1205': 'Latest',
  '1206': 'Earliest',
  '1207': 'Rating descending',
  '1208': 'Seconds ago',
  '1209': 'Minutes ago',
  '1210': 'Hours ago',
  '1211': 'Days ago',
  '1212': 'Months ago',
  '1213': 'Years ago',
  '1214':
    'You are currently using the free version, which does not allow environment deletion. Please visit the official website to purchase the professional version.',
  '1216': 'Collapse',
  '1217': 'Expand all',
  '1218': 'Character',
  '1219': 'Details',
  '1220': 'Split view',
  '1221': 'Tab view',
  '1223': 'Model A Output',
  '1224': 'Model B Output',
  '1225': 'Referee Model Evaluation',
  '1227': 'No matching comparison results found',
  '1228': 'Answer',
  '1229': 'Model A Wins',
  '1230': 'Model B Wins',
  '1231': 'Draw',
  '1232': 'Q&A Pairs',
  '1239': 'Loading',
  '1241': 'File Name',
  '1242': 'Size',
  '1243': 'Annotation Information',
  '1244': 'Category Information',
  '1245': 'Annotation Details',
  '1246': 'Category',
  '1247': 'Area',
  '1248': 'Loading high-resolution image',
  '1474': 'Total:  {total} entries',
  '1490': 'Get Task',
  '1491': 'The chart data failed',
  '1492': 'Parameter failure',
  '1493': 'Failed to obtain parameter data:',
  '1494': 'Hyperparameter comparison',
  '1495': 'Parameter',
  '1516': 'Normal',
  '1517': 'Abnormal',
  '1518': 'Unknown',
  '1519': 'Dataset details',
  '1520': 'Total',
  '1521': 'Data list',
  '1532': 'The current password cannot be empty',
  '1533': 'The new password cannot be empty',
  '1534': 'The new password must be at least 8 characters long',
  '1535': 'The passwords entered twice are inconsistent',
  '1536': 'Password change failed',
  '1537': 'Password change successful',
  '1538': 'Your password has been successfully updated',
  '1539': 'Please check if your current password is correct',
  '1540': 'Please enter your current password and new password',
  '1541': 'Current password',
  '1542': 'New password',
  '1543': 'Password must be at least 8 characters long',
  '1544': 'Confirm new password',
  '1545': 'Submitting...',
  '1546': 'Confirm changes',
  '1561': 'Project code',
  '1564': 'Model Saving Logs',
  '1582': 'Type',
  '1585': 'Warning',
  '1586': 'Data Dashboard',
  '1587': 'Data Overview and Analysis',
  '1590': 'Trained Models',
  '1592': 'Total Volume',
  '1593': 'Fine-tuning Tasks',
  '1594': 'View More',
  '1595': 'Model Evaluation Distribution',
  '1596': 'Model Percentage',
  '1597': 'Base Model Percentage',
  '1598': 'Third-party Model Distribution',
  '1599': 'Filter Criteria',
  '1600': 'Selected {count} items',
  '1601': 'Clear Filters',
  '1602': 'Search',
  '1632': 'Logging out...',
  '1633': 'Logout successful',
  '1634': 'Logout failed, please try again',
  '1635': 'Please enter a valid integer, for example: 123, -456, 0',
  '1636':
    'Please enter a valid floating-point number, for example: 3.14, -2.5, 1e-3',
  '1637': 'Please enter a boolean value: true or false',
  '1638': 'Please enter a non-empty string',
  '1639': 'Please enter a valid parameter value',
  '1640': 'For example: 123, -456, 0',
  '1641': 'For example: 3.14, -2.5, 1e-3',
  '1642': 'Enter true or false',
  '1643': 'Enter any text',
  '1644': 'Please enter the parameter value',
  '1645': 'Parameter file name',
  '1646': 'Company ID',
  '1647': 'Are you sure you want to delete the parameter file "{name}"?',
  '1648': 'Parameter label',
  '1649': 'Parameter name',
  '1650': 'Parameter value',
  '1651': 'Are you sure you want to delete the parameter "{name}"?',
  '1652': 'Model parameter management',
  '1653': 'Manage and configure model training parameter files',
  '1654': 'Create new parameter file',
  '1655': 'Create',
  '1656': 'Please enter the parameter file name',
  '1657': 'Name cannot exceed 50 characters',
  '1658': 'Parameter file details',
  '1659': 'Close',
  '1660': 'Basic information',
  '1662': 'Parameter list',
  '1663': 'Parameter',
  '1664': 'New Parameter',
  '1665': 'Please enter the parameter label',
  '1666': 'Label cannot exceed 50 characters',
  '1667': 'Please enter the parameter name',
  '1668': 'Parameter name cannot exceed 50 characters',
  '1669':
    'Parameter name can only contain letters, numbers, and underscores, and cannot start with a number',
  '1670': 'Parameter type',
  '1671': 'Please select the parameter type',
  '1672': 'Parameter value cannot exceed 200 characters',
  '1673': 'Please select the parameter type first',
  '1735': 'No .jsonl file found in the selected folder',
  '1736': 'File uploaded successfully',
  '1741': 'Please select a JSONL file as the display file first',
  '1742': 'Return to dataset list',
  '1743': 'Upload new dataset',
  '1744': 'Single file upload',
  '1746': 'Multimodal dataset upload',
  '1747': 'Upload folder',
  '1748': 'Select a multimodal dataset folder',
  '1749':
    'Click the button below to select the entire folder containing multimodal data',
  '1750': '💡 Tip: Ensure that the folder contains a .jsonl file',
  '1751': 'Select folder',
  '1752': 'Folder selected, with a total of {count} files',
  '1753': 'Cancel upload',
  '1754': 'Structure of the selected folder',
  '1755': 'To reselect, click the "Cancel upload" button above',
  '1756': 'Select the JSONL file for display',
  '1757':
    "Find the following JSONL files in the uploaded folder and select one as the dataset's display file:",
  '1759': 'Failed to get model list',
  '1760': 'Please enter the model name',
  '1761': 'Please enter the model type',
  '1762': 'Please enter the training framework',
  '1792': 'Please select a model before engaging in a conversation',
  '1793': 'Select Model',
  '1794':
    'You are an intelligent, reliable, polite, and efficient AI assistant. Please provide clear, organized, and accurate answers according to user requirements.',
  '1795': 'Session creation failed',
  '1796': 'Failed to create session, use alternative solution:',
  '1797': 'Session creation failed, using locally generated session ID',
  '1798': 'Invalid local URL:',
  '1799': 'The image does not have a valid URL:',
  '1800': 'Failed to delete session:',
  '1801': 'Delete session failed, but will continue to clear local data',
  '1802': 'Cancel comparison',
  '1803': 'synchronization parameters',
  '1804': 'Preview',
  '1805': 'Uploading ..',
  '1807': 'Please ensure that both models have been selected ..',
  '1808': 'Please select the model first ..',
  '1809':
    'The content is generated by AI and cannot be guaranteed to be true and accurate. It is for reference only and please comply with this platform',
  '1810': 'User Agreement',
  '1811': 'And relevant regulations on national network information security',
  '1901': 'Failed to get model category',
  '1902': 'Model category',
  '1903': 'Please select a model category',
  '1904': 'No downloadable compressed file',
  '1905': 'Zip file downloaded successfully',
  '1906': 'Please compress the folder first',
  '1907': 'Compressed file uploaded successfully',
  '1908': 'An error occurred while uploading the compressed file:',
  '1909': 'An error occurred while uploading the compressed file',
  '1910': 'Please enter the dataset name first',
  '1911': 'Compressing folder...',
  '1912': 'Folder compressed, starting upload...',
  '1913': 'Automatic compression and upload failed, please try again',
  '1914': 'Please upload the compressed file first',
  '1915': 'Please select a JSONL file first',
  '1916': 'Multimodal dataset created successfully',
  '1917': 'An error occurred while creating the dataset:',
  '1918': 'An error occurred while creating the dataset',
  '1919':
    '📦 The folder will be automatically compressed into a .zip file and then uploaded',
  '1920': 'Upload status',
  '1921': 'Compressing and uploading files...',
  '1922': 'Download {fileName}',
  '1923': 'Compressed file has been uploaded',
  '1924': 'Submit dataset',
  '1925': 'An error occurred while uploading the file:',
  '1926': 'An error occurred while uploading the file',
  '1927': 'Please upload a file first',
  '1928': 'Dataset created successfully',
  '1929': 'Uploading...',
  '1930':
    'Supports single file upload, file size not exceeding 100MB, supports formats like .jsonl',
  '1931': 'Uploaded files',
  '1932': 'Hide annotations',
  '1933': 'Show annotations',
  '1934': 'Annotate image',
  '1935': 'Total',
  '1936': 'annotated objects',
  '1937': 'Legend',
  '1938': 'Total',
  '1939': 'annotations',
  '1940': 'Image {imageNumber}',
  '1941': 'Image {imageNumber} annotations',
  '1942': 'Chat messages',
  '1943': 'Image annotation information (COCO format)',
  '1975': 'Successfully uploaded {length} images',
  '1976': 'Image upload failed:{message}',
  '1977':
    'The current model does not support image upload. Please select a model of the vision language type',
  '1978': 'Session not created, please try again later',
  '1979': 'Successfully pasted and uploaded {length} images',
  '1980': 'Image upload failed:{msg}',
  '1981': 'Paste image upload failed:',
  '1982': 'The image size cannot exceed 5MB',
  '1983':
    'Only supports uploading image files in JPG, JPEG, PNG, or GIF formats',
  '1985': 'picture',
  '1986': 'characters',
  '1987': 'Hide bounding box',
  '1988': 'Display bounding box',
  '1989': 'Hide segmentation area',
  '1990': 'Display segmentation area',
  '1991': 'Split information',
  '1992': 'polygon',
  '1993': 'Number of vertices',
  '1994': 'Please select the prompt variable',
  '1995': 'Selected image fields (can be dragged and dropped to adjust order)',
  '2028': 'Failed to obtain image:',
  '2029': 'image upload',
  '2030': 'Click to upload or drag the image here',
  '2031':
    'Supports JPG, JPEG, PNG formats, with a single file size not exceeding 5MB',
  '2032': 'NAME',
  '2033': 'Configure the prompt words for the referee model',
  '2034': 'problem',
  '2035': 'User image input list',
  '2036': 'Referee image input list',
  '2037': 'Failed to obtain high-definition image:',
  '2038': 'Roller zoom',
  '2039': 'Right click drag and drop',
  '2040': 'The {current} sheet, a total of {total} sheets',
  '2041': 'Sheet 1 of 1',
  '2074': 'Please enter the filtering value',
  '2075': 'Please enter the name of Model A',
  '2076': 'Please enter the name of Model B',
  '2077': 'Please enter the name of the referee model',
  '2078': 'Please enter the system prompt word',
  '2079': 'Please enter the current password',
  '2080': 'Please enter the model template',
  '2081': 'Please enter your phone number',
  '2082': 'Please enter your email address',
  '2083': 'Please enter a model description',
};
