'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Input, Select, Button, Form, Tag } from 'antd';
import KeepAlive, { AliveScope, useAliveController } from 'react-activation';
import { DragOutlined, CloseOutlined } from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const { TextArea } = Input;

// 可拖拽的图片字段组件
interface SortableImageFieldProps {
  id: string;
  field: string;
  onRemove: (field: string) => void;
}

const SortableImageField: React.FC<SortableImageFieldProps> = ({
  id,
  field,
  onRemove,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`inline-flex items-center gap-1 p-1.5 bg-blue-50 border border-blue-200 rounded-md mr-2 mb-2 ${
        isDragging ? 'shadow-lg' : ''
      }`}>
      <div
        {...attributes}
        {...listeners}
        className='cursor-move text-gray-500 hover:text-gray-700'>
        <DragOutlined className='text-xs' />
      </div>
      <Tag color='blue' className='m-0 text-xs'>
        {field}
      </Tag>
      <Button
        type='text'
        size='small'
        icon={<CloseOutlined className='text-xs' />}
        onClick={() => onRemove(field)}
        className='text-gray-400 hover:text-red-500 p-0 w-4 h-4 flex items-center justify-center'
      />
    </div>
  );
};

interface PromptEditorProps {
  status: string;
  paramsOptions: {
    cup: { label: string; value: string }[];
    jsp: { label: string; value: string }[];
    jup: { label: string; value: string }[];
  };
  datasetType?: 'chat' | 'vision';
  resetTrigger?: number; // 重置触发器
}

export function PromptEditor({
  status,
  paramsOptions,
  datasetType = 'chat',
  resetTrigger = 0,
}: PromptEditorProps) {
  // 使用useAliveController获取KeepAlive控制函数
  const { refresh } = useAliveController();

  // 当数据集类型变化时，刷新KeepAlive缓存，确保拖拽部分被重置
  useEffect(() => {
    if (refresh && typeof refresh === 'function') {
      refresh('judgePrompts');
    }
  }, [datasetType, refresh]);

  return (
    <AliveScope>
      <div className=' flex flex-col gap-4'>
        <div className='border border-gray-300 rounded-lg p-8 bg-white space-y-6 mt-4'>
          <div>
            <div className='text-2xl font-bold '>{t('模型提示词')}</div>
            <div className='text-gray-500 mt-2'>
              {t('配置模型A和模型B的提示词')}
            </div>
          </div>

          <div className='space-y-6'>
            <div className='space-y-2'>
              <Form.Item
                name='candidateSystemPrompt'
                initialValue={
                  'You are a professional AI assistant. For each question provided by the user, you should give accurate and expert answers.'
                }>
                <CustomTextArea
                  title={t('系统提示词')}
                  tip={t('系统提示词用于设置模型的行为和角色')}
                  options={paramsOptions.jsp} // 使用系统相关的选项
                  datasetType={datasetType}
                  resetTrigger={resetTrigger}
                  imageFieldName='candidateSystemImages'
                />
              </Form.Item>
              {/* 隐藏的系统提示词图片字段 */}
              <Form.Item name='candidateSystemImages' initialValue={[]}>
                <Input type='hidden' />
              </Form.Item>
            </div>

            <div className='space-y-2'>
              <Form.Item name='candidateUserPrompt' initialValue={'{{input}}'}>
                <CustomTextArea
                  tip={t('用户提示词是您想要模型回答的实际问题或任务')}
                  title={t('用户提示词')}
                  options={paramsOptions.cup}
                  datasetType={datasetType}
                  resetTrigger={resetTrigger}
                  imageFieldName='candidateUserImages'
                />
              </Form.Item>
              {/* 隐藏的用户提示词图片字段 */}
              <Form.Item name='candidateUserImages' initialValue={[]}>
                <Input type='hidden' />
              </Form.Item>
            </div>
          </div>
        </div>

        {(status === 'referee' || status === 'evaluate') && (
          <KeepAlive name='judgePrompts'>
            <div className='border border-gray-300 rounded-lg p-8 bg-white'>
              <div className='space-y-6'>
                <div>
                  <div className='text-2xl font-bold'>
                    {t('裁判模型提示词')}
                  </div>
                  <div className='text-gray-500 mt-2'>
                    {t('配置裁判模型的提示词')}
                  </div>
                </div>
                <div className='space-y-2'>
                  <Form.Item
                    name='judgeSystemPrompt'
                    initialValue={`You are a professional evaluator tasked with comparing the responses of two large language models and determining which one is better. Please assess based on accuracy, logic, clarity of expression, and completeness.Make your judgment solely based on the provided responses, without relying on any external knowledge.You must return the evaluation result in the following JSON format: {"better": "A",  // or "B","reason": "Briefly explain why you chose this model's response, in no more than 3 sentences."}`}>
                    <CustomTextArea
                      title={t('裁判系统提示词')}
                      tip={t('设置裁判模型的行为和评估标准')}
                      options={paramsOptions.jsp}
                      datasetType={datasetType}
                      resetTrigger={resetTrigger}
                      imageFieldName='judgeSystemImages'
                    />
                  </Form.Item>
                  {/* 隐藏的裁判系统提示词图片字段 */}
                  <Form.Item name='judgeSystemImages' initialValue={[]}>
                    <Input type='hidden' />
                  </Form.Item>
                </div>

                <div className='space-y-2'>
                  <Form.Item
                    name='judgeUserPrompt'
                    initialValue={`Here is the question along with the responses from two models. Please determine which model's response is better and briefly explain the reason.
Question: {{CandidateUserPrompt}}
Response from Model A:
{{ResponseA}}
Response from Model B:
{{ResponseB}}`}>
                    <CustomTextArea
                      title={t('裁判用户提示词')}
                      tip={t('指导裁判模型如何评估模型的回答')}
                      options={paramsOptions.jup}
                      datasetType={datasetType}
                      resetTrigger={resetTrigger}
                      imageFieldName='judgeUserImages'
                    />
                  </Form.Item>
                  {/* 隐藏的裁判用户提示词图片字段 */}
                  <Form.Item name='judgeUserImages' initialValue={[]}>
                    <Input type='hidden' />
                  </Form.Item>
                </div>
              </div>
            </div>
          </KeepAlive>
        )}
      </div>
    </AliveScope>
  );
}

type CustomTextAreaType = {
  onChange?: any;
  value?: string;
  tip?: string;
  title?: string;
  options: { value: any; label: string }[];
  datasetType?: 'chat' | 'vision';
  resetTrigger?: number; // 重置触发器
  imageFieldName?: string; // 对应的图片字段名
  [key: string]: any;
};

const CustomTextArea: React.FC<CustomTextAreaType> = ({
  tip,
  title,
  value,
  onChange,
  options,
  // datasetType = 'chat',
  resetTrigger = 0,
  imageFieldName,
}) => {
  const [selectedOption, setSelectedOption] = useState<any>(null);
  const [selectedImageFields, setSelectedImageFields] = useState<string[]>([]);
  const textAreaRef = useRef<any>(null);
  const form = Form.useFormInstance(); // 获取表单实例

  // 当 resetTrigger 变化时重置图片字段（仅在数据集变化时）
  useEffect(() => {
    if (resetTrigger > 0) {
      // 强制清空已选择的图片字段，无论长度是否大于0
      setSelectedImageFields([]);
      // 重置选择的选项
      setSelectedOption(null);
      // 清空对应的表单字段
      if (imageFieldName) {
        form.setFieldValue(imageFieldName, []);
      }
      console.log(
        '[CustomTextArea] Reset image fields and selected option due to dataset change',
      );
    }
  }, [resetTrigger, imageFieldName, form]);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleInsertText = (textToInsert: string) => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentValue = value || '';
    const newValue = `${currentValue.slice(0, start)}${textToInsert}${currentValue.slice(end)}`;
    onChange?.(newValue);

    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + textToInsert.length,
        start + textToInsert.length,
      );
    }, 0);
  };

  const handleAddImageField = (fieldToAdd: string) => {
    if (fieldToAdd && !selectedImageFields.includes(fieldToAdd)) {
      const newFields = [...selectedImageFields, fieldToAdd];
      setSelectedImageFields(newFields);
      // 更新对应的表单字段
      if (imageFieldName) {
        form.setFieldValue(imageFieldName, newFields);
        console.log(
          `[CustomTextArea] Updated ${imageFieldName} with:`,
          newFields,
        );
      }
    }
  };

  // 统一的添加处理函数
  const handleAddOption = () => {
    if (!selectedOption) return;

    if (selectedOption.type === 2) {
      // 图片类型：添加到图片字段列表
      handleAddImageField(selectedOption.originalLabel || selectedOption.value);
    } else {
      // 文字类型：插入到文本区域
      const textToInsert = `{{${selectedOption.originalLabel || selectedOption.value}}}`;
      handleInsertText(textToInsert);
    }

    // 清空选择
    setSelectedOption(null);
  };

  const handleRemoveImageField = (field: string) => {
    const newFields = selectedImageFields.filter((f) => f !== field);
    setSelectedImageFields(newFields);
    // 更新对应的表单字段
    if (imageFieldName) {
      form.setFieldValue(imageFieldName, newFields);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = selectedImageFields.indexOf(active.id);
      const newIndex = selectedImageFields.indexOf(over.id);
      const newFields = arrayMove(selectedImageFields, oldIndex, newIndex);
      setSelectedImageFields(newFields);
      // 更新对应的表单字段
      if (imageFieldName) {
        form.setFieldValue(imageFieldName, newFields);
      }
    }
  };

  return (
    <div className='space-y-2'>
      <div className='text-sm font-medium'>{title}</div>

      {/* 统一的选择器 - 显示所有提示变量 */}
      <div className='flex gap-2 mb-2'>
        <Select
          placeholder={t('请选择提示变量')}
          className='w-[300px]'
          value={selectedOption?.value}
          onChange={(value) => {
            const option = options.find((opt) => opt.value === value);
            setSelectedOption(option);
          }}
          options={options}
          optionRender={(option) => option.data.label}
        />
        <Button
          onClick={handleAddOption}
          style={{ height: '30px' }}
          disabled={!selectedOption}>
          {t('添加')}
        </Button>
      </div>

      {/* 已选择的图片字段列表 - 水平布局，独立显示 */}
      {selectedImageFields.length > 0 && (
        <div className='p-3 bg-gray-50 border border-gray-200 rounded-md'>
          <div className='text-xs text-gray-600 mb-2'>
            {t('已选择的图片字段（可拖拽调整顺序）')}:
          </div>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}>
            <SortableContext
              items={selectedImageFields}
              strategy={horizontalListSortingStrategy}>
              <div className='flex flex-wrap'>
                {selectedImageFields.map((field) => (
                  <SortableImageField
                    key={field}
                    id={field}
                    field={field}
                    onRemove={handleRemoveImageField}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>
      )}

      <TextArea
        ref={textAreaRef}
        value={value}
        rows={5}
        onChange={(value) => {
          onChange?.(value.target.value);
        }}
      />
      <p className='text-xs text-gray-500 mt-2'>{tip}</p>
    </div>
  );
};
