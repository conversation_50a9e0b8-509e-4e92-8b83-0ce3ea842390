import React, { useEffect, useState } from 'react';
import TabCard from './components/TabCard';
import ModelInput from './components/ModelInput';
import { PromptEditor } from './components/Prompts';
import { ResultCard } from './components/ResultCard';
import { useNavigate, useLocation } from 'react-router-dom';
import { Form, InputNumber, Button, Select, Checkbox } from 'antd';
import { useAuthStore } from '@/store/features';
import { ModelList } from '@/pages/PromptWords/type';
import { getModelInfoListForSelect } from '@/pages/PromptWords/api';
import { getEvalTaskConfig } from '../../api';
import { EvalConfig } from '../../type';
import * as _ from 'lodash';
import {
  getDatasetListForSelect,
  getPromptVar,
  pairEvalByLLM,
  getEvalPreviewImages,
} from '../../api';
import {
  EyeOutlined,
  SendOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons';

const RunModelEvaluation: React.FC = () => {
  const [form] = Form.useForm();
  const testDatasetUuid = Form.useWatch('testDatasetUuid', form);
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = location.state || { id: 0 };

  const { token } = useAuthStore();

  const [status, setStatus] = useState('referee');
  const [datasetOptions, setDatasetOptions] = useState<any[]>([]);
  const [paramsOptions, setParamsOptions] = useState<{
    cup: { label: string; value: string }[];
    jsp: { label: string; value: string }[];
    jup: { label: string; value: string }[];
  }>({
    cup: [],
    jsp: [],
    jup: [],
  });
  const [isShowPreview, setIsShowPreview] = useState<boolean>(false);
  const [modelList, setModelList] = useState<ModelList[]>([]);

  const [datasetsIsAll, setDatasetsIsAll] = useState<boolean>(true);

  const [tableSourceMap, setTableSourceMap] = useState<
    Map<number, Map<string, string>>
  >(new Map());
  const [previewItemNum, setPreviewItemNum] = useState<number>(1);
  const [previewImages, setPreviewImages] = useState<Array<{
    candidateUserImages: string[];
    judgeUserImages: string[];
  }>>([]);
  const [modelNames, setModelNames] = useState<{
    modelA_name: string;
    modelB_name: string;
    judge_name: string;
  }>({
    modelA_name: '',
    modelB_name: '',
    judge_name: '',
  });

  // 新增状态：数据集类型
  const [datasetType, setDatasetType] = useState<'chat' | 'vision'>('chat');
  // 重置计数器，用于触发PromptEditor组件重置
  const [resetTrigger, setResetTrigger] = useState(0);

  const getEvalType = (status: string): number => {
    const EvalObj: { [key: string]: number } = {
      referee: 0,
      compare: 1,
      evaluate: 3,
      single: 2,
    };
    return EvalObj[status];
  };

  // 根据数据集UUID从已加载的数据集列表中获取类型
  const getDatasetTypeByUuid = (datasetUuid: string): 'chat' | 'vision' => {
    const dataset = datasetOptions.find(
      (option) => option.value === datasetUuid,
    );
    if (dataset && dataset.type === 2) {
      return 'vision';
    }
    return 'chat';
  };

  const changeParams = () => {
    if (isShowPreview) {
      setIsShowPreview(false);
      setTableSourceMap(new Map());
    }
  };

  const getDataset = async () => {
    try {
      const res = await getDatasetListForSelect();
      if (!res.code) {
        // 为数据集添加类型标签，基于真实的API数据
        const enhancedDatasets = res.data.list.map((dataset: any) => ({
          ...dataset,
          label: (
            <div className='flex items-center gap-2'>
              <span>{dataset.label || dataset.datasetName}</span>
              <span
                className={`px-2 py-0.5 text-xs rounded ${
                  dataset.type === 2
                    ? 'bg-green-100 text-green-600'
                    : 'bg-blue-100 text-blue-600'
                }`}>
                {dataset.type === 2 ? t('图片') : t('文字')}
              </span>
            </div>
          ),
        }));

        setDatasetOptions(enhancedDatasets);
      }
    } catch (error) {
      console.error(error);
      // 如果API调用失败，设置空数组
      setDatasetOptions([]);
    }
  };

  const getModelInfoListForSelectFetch = async (
    modelType: 'chat' | 'vision-language' = 'chat',
  ) => {
    try {
      const res = await getModelInfoListForSelect(modelType);
      if (!res.code) {
        setModelList(res.data.list);
      }
    } catch (error) {
      console.error('Error fetching model config list:', error);
    }
  };

  const getParamsOptions = async (dataUuid: string) => {
    if (dataUuid === undefined || dataUuid === null) return;
    try {
      const res = Promise.all([
        getPromptVar(dataUuid, 'CandidateUserPrompt'),
        getPromptVar(dataUuid, 'JudgeSystemPrompt'),
        getPromptVar(dataUuid, 'JudgeUserPrompt'),
      ]);
      const [cup, jsp, jup] = await res;

      // 处理新的API响应结构，为每个选项添加类型标签
      const enhanceOptions = (options: any[]) => {
        return options.map((option) => ({
          ...option,
          label: (
            <div className='flex items-center gap-2'>
              <span>{option.label}</span>
              <span
                className={`px-2 py-0.5 text-xs rounded ${
                  option.type === 2
                    ? 'bg-green-100 text-green-600'
                    : 'bg-blue-100 text-blue-600'
                }`}>
                {option.type === 2 ? t('图片') : t('文字')}
              </span>
            </div>
          ),
          originalLabel: option.label, // 保存原始标签用于插入
        }));
      };

      setParamsOptions({
        cup: enhanceOptions(cup.data.list),
        jsp: enhanceOptions(jsp.data.list),
        jup: enhanceOptions(jup.data.list),
      });
    } catch (error) {
      console.error(error);
    }
    return [];
  };

  const getModel = (value: string) => {
    const [groupName, modelIndex] = value.split('$');
    const model = modelList.find((item) => item.groupName === groupName);
    if (model && model.modelInfoList[Number(modelIndex)]) {
      return {
        modelConfigId: model.modelInfoList[Number(modelIndex)].modelConfigId,
        modelName: model.modelInfoList[Number(modelIndex)].modelName,
        modelPath: model.modelInfoList[Number(modelIndex)].modelPath,
        taskUuid: model.modelInfoList[Number(modelIndex)].taskUuid,
      };
    } else return null;
  };

  const getModelKey = (modelConfig: EvalConfig['modelAConfig']) => {
    let modelKey = null;
    if (modelConfig.modelConfigId !== 0) {
      for (const model of modelList) {
        const flag = model.modelInfoList.findIndex(
          (item) => item.modelConfigId === modelConfig.modelConfigId,
        );
        if (flag !== -1) {
          modelKey = `${model.groupName}$${flag}`;
        }
      }
    } else {
      for (const model of modelList) {
        const flag = model.modelInfoList.findIndex(
          (item) => item.taskUuid === modelConfig.taskUuid,
        );
        if (flag !== -1) {
          modelKey = `${model.groupName}$${flag}`;
        }
      }
    }
    return modelKey;
  };

  const onSubmit = async (
    fetch: (params: any) => Promise<any>,
    options?: any,
  ) => {
    const {
      modelA_base,
      modelA_baseUuid,
      modelB_base,
      modelB_baseUuid,
      judgeModel_base,
      judgeModel_baseUuid,
      datasetItemNum,
      candidateSystemImages,
      candidateUserImages,
      judgeSystemImages,
      judgeUserImages,
      ...rest
    } = await form.validateFields();

    const params = {
      ...rest,
      modelA: getModel(modelA_base),
      ...(modelB_base && {
        modelB: getModel(modelB_base),
      }),
      ...(judgeModel_base && {
        judgeModel: getModel(judgeModel_base),
      }),
      candidateSystemImages: candidateSystemImages || [],
      candidateUserImages: candidateUserImages || [],
      judgeSystemImages: judgeSystemImages || [],
      judgeUserImages: judgeUserImages || [],
      preview: false,
      previewItemNum: 0,
      evalType: getEvalType(status),
      datasetItemNum: datasetsIsAll ? 0 : datasetItemNum,
    };

    // 添加调试信息
    console.log('[Form Submission] Dataset Type:', datasetType);
    console.log('[Form Submission] Complete Params:', params);
    console.log('[Form Submission] Image Fields:', {
      candidateSystemImages: candidateSystemImages || [],
      candidateUserImages: candidateUserImages || [],
      judgeSystemImages: judgeSystemImages || [],
      judgeUserImages: judgeUserImages || [],
    });

    setModelNames({
      modelA_name: params.modelA?.modelName || '',
      modelB_name: params.modelB?.modelName || '',
      judge_name: params.judgeModel?.modelName || '',
    });

    const finalParams = _.omitBy({ ...params, ...options }, _.isUndefined);
    fetch(finalParams);
  };
  const onFinish = async () => {
    await onSubmit(pairEvalByLLM);
    navigate('/modelEvaluation');
  };

  const handleStreamingPreview = async (params: any) => {
    setIsShowPreview(true);
    try {
      const response = await fetch('/api/eval/pairEvalByLLM/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'text/event-stream',
          'X-token': token,
        } as any,
        body: JSON.stringify(params),
      });

      if (!response.body) return;

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value);
        const lines = chunk
          .split('\n')
          .filter((line) => line.trim())
          .map((line) => line.replace(/^data:\s*/, ''));

        for (const line of lines) {
          try {
            const { text, contentType, seq } = JSON.parse(line);
            if (text === undefined) continue;
            const idx = seq - 1;
            setTableSourceMap((prev) => {
              const newMap = new Map(prev);
              if (!newMap.has(idx)) {
                newMap.set(idx, new Map());
              }
              const innerMap = new Map(newMap.get(idx));
              innerMap.set(
                contentType,
                (innerMap.get(contentType) || '') + text,
              );
              newMap.set(idx, innerMap);
              return newMap;
            });
          } catch (e) {
            console.error('Failed to parse chunk:', e);
          }
        }
      }
    } catch (error) {
      console.error('Preview failed:', error);
    }
  };

  const getEvalTaskConfigFetch = async () => {
    try {
      const res = await getEvalTaskConfig({
        taskUuid: id,
      });
      if (!res.code) {
        const detail = res.data.result as EvalConfig;

        const evalTypeMap: { [key: number]: string } = {
          0: 'referee',
          1: 'compare',
          2: 'single',
          3: 'evaluate',
        };
        setStatus(evalTypeMap[detail.evalType]);
        setDatasetsIsAll(detail.datasetItemNum === 0);

        const modelA = getModelKey(detail.modelAConfig);
        const modelB = getModelKey(detail.modelBConfig);
        const judgeModel = getModelKey(detail.judgeModelConfig);

        const params = {
          ...detail,
          testDatasetUuid: detail.datasetUuid,
          datasetItemNum: detail.datasetItemNum,
          modelA_base: modelA,
          modelB_base: modelB,
          judgeModel_base: judgeModel,
          modelA_baseUuid: detail.modelAConfig.baseUuid,
          modelB_baseUuid: detail.modelBConfig.baseUuid,
          judgeModel_baseUuid: detail.judgeModelConfig.baseUuid,
        };
        form.setFieldsValue(params);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (id === 0) return;
    getEvalTaskConfigFetch();
  }, [id, modelList]);

  useEffect(() => {
    getDataset();
  }, []);

  useEffect(() => {
    getParamsOptions(testDatasetUuid);
    // 根据选择的数据集设置类型
    if (testDatasetUuid) {
      const newDatasetType = getDatasetTypeByUuid(testDatasetUuid);
      setDatasetType(newDatasetType);
      console.log(
        `[Dataset Selection] UUID: ${testDatasetUuid}, Type: ${newDatasetType}`,
      );

      // 根据数据集类型请求相应的模型列表
      const modelType =
        newDatasetType === 'vision' ? 'vision-language' : 'chat';
      getModelInfoListForSelectFetch(modelType);

      // 触发PromptEditor组件重置选择器状态和图片字段
      setResetTrigger((prev) => prev + 1);

      // 当数据集变化时，清空用户提示词、裁判系统提示词和裁判用户提示词
      form.setFieldsValue({
        candidateUserPrompt: '{{input}}',
        judgeSystemPrompt: `You are a professional evaluator tasked with comparing the responses of two large language models and determining which one is better. Please assess based on accuracy, logic, clarity of expression, and completeness.Make your judgment solely based on the provided responses, without relying on any external knowledge.You must return the evaluation result in the following JSON format: {"better": "A",  // or "B","reason": "Briefly explain why you chose this model's response, in no more than 3 sentences."}`,
        judgeUserPrompt: `Here is the question along with the responses from two models. Please determine which model's response is better and briefly explain the reason.
Question: {{CandidateUserPrompt}}
Response from Model A:
{{ResponseA}}
Response from Model B:
{{ResponseB}}`,
      });

      // 重置预览结果、预览数量、数据集大小选择和评估模式
      setTableSourceMap(new Map());
      setIsShowPreview(false);
      setPreviewItemNum(1);
      setPreviewImages([]);
      setDatasetsIsAll(false);
      setStatus('referee');
      form.setFieldsValue({
        datasetItemNum: 10,
        // 重置模型选择
        modelA_base: undefined,
        modelA_baseUuid: undefined,
        modelB_base: undefined,
        modelB_baseUuid: undefined,
        judgeModel_base: undefined,
        judgeModel_baseUuid: undefined,
      });

      // 注意：KeepAlive缓存的刷新在Prompts.tsx组件中处理
    }
  }, [testDatasetUuid, datasetOptions]);

  return (
    <div className='w-full p-4 flex flex-col gap-4'>
      <Button
        type='link'
        className='w-12'
        icon={<ArrowLeftOutlined />}
        onClick={() => {
          navigate('/modelEvaluation');
        }}>
        {t('返回')}
      </Button>
      <div className='w-full border border-gray-300 rounded-xl p-8 bg-white'>
        <div className='text-2xl font-bold'>{t('评估模型')}</div>
        <div className='text-gray-500 mt-1'>
          {t('选择您想要使用的评估模式')}
        </div>
        <TabCard
          value={status}
          onChange={(value) => {
            setStatus(value);
            changeParams();
          }}
          options={[
            {
              label: t('裁判模式'),
              value: 'referee',
              description: t('使用模型A、模型B和裁判模型进行评估'),
            },
            {
              label: t('对比模式'),
              value: 'compare',
              description: t('仅使用模型A和模型B进行对比'),
            },
            {
              label: t('评估模式'),
              value: 'evaluate',
              description: t('利用裁判模型评估模型A的表现'),
            },
            {
              label: t('单模型模式'),
              value: 'single',
              description: t('仅使用模型A进行输出'),
            },
          ]}
        />
      </div>

      <Form
        layout='vertical'
        form={form}
        onValuesChange={() => {
          changeParams();
        }}>
        <div className='w-full border border-gray-300 rounded-xl p-8 bg-white mb-4'>
          <div className='text-2xl font-bold mb-2'>
            <span className='text-red-500 mr-1 text-sm'>*</span>
            {t('数据集选择')}
          </div>
          <div className='text-gray-500 mb-6'>
            {t('选择要评估的基础数据集')}
          </div>
          <Form.Item
            name='testDatasetUuid'
            rules={[{ required: true, message: t('请选择数据集') }]}>
            <Select options={datasetOptions} style={{ width: 300 }} />
          </Form.Item>

          <div className='space-y-2'>
            <div className='text-sm font-medium'>{t('数据集大小')}</div>
            <div className='text-gray-500 mt-2'>
              {t('配置评估数据集的大小')}
            </div>
            <div className='flex items-center gap-2 mb-2'>
              <Checkbox
                checked={datasetsIsAll}
                onChange={(e) => {
                  setDatasetsIsAll(e.target.checked);
                }}
              />
              <span
                className={`text-sm font-medium ${!datasetsIsAll && 'line-through'}`}>
                {t('选择全部')}
              </span>
            </div>
          </div>
          {!datasetsIsAll ? (
            <Form.Item name='datasetItemNum' initialValue={10}>
              <InputNumber />
            </Form.Item>
          ) : null}
        </div>

        <div className='w-full border border-gray-300 rounded-xl p-4 bg-white'>
          <ModelInput
            status={status as 'referee' | 'compare' | 'evaluate' | 'single'}
            modelList={modelList}
          />
        </div>
        <div>
          <PromptEditor
            status={status}
            paramsOptions={paramsOptions}
            datasetType={datasetType}
            resetTrigger={resetTrigger}
          />
        </div>
      </Form>
      <div className='w-full grid grid-cols-2 gap-2'>
        <div className='col-span-1 border border-gray-300 rounded-xl p-8 bg-white '>
          <div className='text-2xl font-bold'>{t('预览设置')}</div>
          <div className='text-gray-400 mt-2'>
            {t(
              '设置预览数量并查看部分结果,预览结果在这个页面下方显示,向下滑动查看',
            )}
          </div>
          <div className='flex gap-2 items-center font-bold mt-4'>
            <div>{t('预览数量:')}</div>
            <InputNumber
              value={previewItemNum}
              max={5}
              min={1}
              onChange={(value: number | null) => setPreviewItemNum(value || 1)}
            />
          </div>
          <Button
            className='w-full mt-4 text-xl'
            icon={<EyeOutlined />}
            onClick={async () => {
              setTableSourceMap(new Map());
              
              // 获取表单数据
              const formValues = form.getFieldsValue();
              
              // 获取预览图片
              try {
                const imageResponse = await getEvalPreviewImages({
                  datasetItemNum: previewItemNum.toString(),
                  datasetUuid: formValues.testDatasetUuid || '',
                  candidateUserImages: formValues.candidateUserImages || [],
                  judgeUserImages: formValues.judgeUserImages || [],
                });
                
                if (imageResponse.code === 0 && imageResponse.data?.list) {
                  setPreviewImages(imageResponse.data.list);
                }
              } catch (error) {
                console.error('获取预览图片失败:', error);
              }
              
              onSubmit(handleStreamingPreview, {
                preview: true,
                previewItemNum: previewItemNum,
              });
            }}>
            {t('预览结果')}
          </Button>
        </div>
        <div className='col-span-1 border border-gray-300 rounded-xl p-8 bg-white '>
          <div className='text-2xl font-bold'>{t('提交评估')}</div>
          <div className='text-gray-400 mt-2'>
            {t('提交所有数据进行完整评估')}
          </div>
          <Button
            type='primary'
            className='w-full mt-4 text-xl'
            onClick={onFinish}
            icon={<SendOutlined />}>
            {t('提交评估')}
          </Button>
        </div>
      </div>
      {isShowPreview && (
        <div className='w-full space-y-6'>
          <div className='w-full border border-gray-300 rounded-xl p-8 bg-white'>
            <ResultCard
              evaluationMode={status}
              modelA={modelNames.modelA_name}
              modelB={modelNames.modelB_name}
              tableSourceMap={tableSourceMap}
              previewImages={previewImages}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default RunModelEvaluation;
