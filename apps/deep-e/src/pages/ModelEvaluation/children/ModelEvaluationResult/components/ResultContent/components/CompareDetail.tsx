'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Image } from 'antd';
import { CollapsibleText } from './CollapsibleText';
import { ModelResponse } from '../../../../../type';
import * as _ from 'lodash';

// 图片列表显示组件
function ImageListDisplay({
  images,
  title,
}: {
  images: string[];
  title: string;
}) {
  if (!images || images.length === 0) {
    return null;
  }

  return (
    <Card className='mb-6'>
      <CardHeader className='pb-2'>
        <CardTitle className='text-lg'>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='flex flex-wrap gap-3'>
          {images.map((base64Image, index) => (
            <div key={index} className='flex-shrink-0'>
              <Image
                src={`data:image/jpeg;base64,${base64Image}`}
                alt={`${title} ${index + 1}`}
                width={100}
                height={100}
                className='object-cover rounded-lg border border-gray-200'
                style={{
                  width: '100px',
                  height: '100px',
                }}
                preview={{
                  mask: t('预览'),
                }}
              />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface ComparisonDetailProps {
  comparison: ModelResponse;
}

export function ComparisonDetail({ comparison }: ComparisonDetailProps) {
  const [viewMode, setViewMode] = useState<'split' | 'tabbed'>('split');

  return (
    <div className='p-6'>
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h2 className='text-2xl font-bold'>{t('详细信息')}</h2>
        </div>

        <div className='flex gap-2'>
          <Badge
            variant={viewMode === 'split' ? 'default' : 'outline'}
            className='cursor-pointer'
            onClick={() => setViewMode('split')}>
            {t('分栏视图')}
          </Badge>
          <Badge
            variant={viewMode === 'tabbed' ? 'default' : 'outline'}
            className='cursor-pointer'
            onClick={() => setViewMode('tabbed')}>
            {t('选项卡视图')}
          </Badge>
        </div>
      </div>

      {/* Question Section */}
      <Card className='mb-6'>
        <CardHeader className='pb-2'>
          <CardTitle className='text-lg'>{t('问题')}</CardTitle>
        </CardHeader>
        <CardContent>
          <CollapsibleText
            text={comparison.candidateUserPrompt}
            maxLength={300}
          />
        </CardContent>
      </Card>

      {/* 用户图片输入列表 - 在模型A/B输出上面 */}
      <ImageListDisplay
        images={comparison.candidateUserImages || []}
        title={t('用户图片输入列表')}
      />

      {viewMode === 'tabbed' ? (
        <TabbedView comparison={comparison} />
      ) : (
        <SplitView comparison={comparison} />
      )}
    </div>
  );
}

function TabbedView({ comparison }: { comparison: ModelResponse }) {
  return (
    <div className='space-y-6'>
      <Tabs defaultValue='modelA' className='w-full'>
        <TabsList
          className={`grid grid-cols-${comparison.evalType === 0 ? 3 : comparison.evalType === 3 ? 1 : 2}`}>
          <TabsTrigger value='modelA'>{t('模型A')}</TabsTrigger>
          {(comparison.evalType === 0 || comparison.evalType === 1) && (
            <TabsTrigger value='modelB'>{t('模型B')}</TabsTrigger>
          )}

          {(comparison.evalType === 0 || comparison.evalType === 3) && (
            <TabsTrigger value='referee'>{t('裁判模型')}</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value='modelA'>
          <ModelOutputCard
            title={t('模型A输出')}
            output={comparison.responseA}
            score={3}
          />
        </TabsContent>

        {(comparison.evalType === 0 || comparison.evalType === 1) && (
          <TabsContent value='modelB'>
            <ModelOutputCard
              title={t('模型B输出')}
              output={comparison.responseB}
              score={3}
            />
          </TabsContent>
        )}

        {!_.isNil(comparison.judgeResponse) && (
          <TabsContent value='referee'>
            {/* 裁判图片输入列表 - 在裁判模型评估上面 */}
            <ImageListDisplay
              images={comparison.judgeUserImages || []}
              title={t('裁判图片输入列表')}
            />
            <Card>
              <CardHeader className='pb-2'>
                <CardTitle className='text-lg'>{t('裁判模型评估')}</CardTitle>
              </CardHeader>
              <CardContent>
                <CollapsibleText
                  text={comparison.judgeResponse}
                  maxLength={300}
                />
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}

function SplitView({ comparison }: { comparison: ModelResponse }) {
  return (
    <div className='space-y-6'>
      <div className='grid grid-cols-2 gap-6'>
        <ModelOutputCard
          title={t('模型A输出')}
          output={comparison.responseA}
          score={3}
        />
        {(comparison.evalType === 0 || comparison.evalType === 1) && (
          <ModelOutputCard
            title={t('模型B输出')}
            output={comparison.responseB}
            score={3}
          />
        )}
      </div>

      {/* 裁判图片输入列表 - 在模型A/B输出下面，裁判模型上面 */}
      <ImageListDisplay
        images={comparison.judgeUserImages || []}
        title={t('裁判图片输入列表')}
      />

      {!_.isNil(comparison.judgeResponse) && (
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-lg'>{t('裁判模型评估')}</CardTitle>
          </CardHeader>
          <CardContent>
            <CollapsibleText text={comparison.judgeResponse} maxLength={300} />
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function ModelOutputCard({
  title,
  output,
  score,
}: {
  title: string;
  output: string;
  score: number;
}) {
  return (
    <Card>
      <CardHeader className='pb-2 flex flex-row items-center justify-between'>
        <CardTitle className='text-lg'>{title}</CardTitle>
        <div className='flex items-center gap-2'>
          <div className='flex'>
            {Array.from({ length: 5 }).map((_, i) => (
              <span
                key={i}
                className={`text-sm ${i < score ? 'text-yellow-500' : 'text-gray-300'}`}>
                ★
              </span>
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <CollapsibleText text={output} maxLength={300} />
      </CardContent>
    </Card>
  );
}
