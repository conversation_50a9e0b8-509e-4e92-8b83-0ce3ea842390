import React, { useCallback, useEffect, useState } from 'react';
import { Modal, Form, Input, Select, Button, Switch } from 'antd';
import { ModelData } from '../types';

interface ConfigModelProps {
  isModalVisible: boolean;
  onCancel: () => void;
  onSubmit: (values: any) => void;
  currentModel: ModelData | null;
  providers: any[];
  modelTypes: any[];
  onTest: (values: any) => Promise<{ pingRes: number; pingTime: number }>;
  form: any;
  isEditModel: boolean;
}
const ConfigModel: React.FC<ConfigModelProps> = ({
  isModalVisible,
  onCancel,
  onSubmit,
  currentModel,
  providers,
  modelTypes,
  onTest,
  form,
  isEditModel,
}) => {
  const [testStatus, setTestStatus] = useState<number>(
    currentModel?.status || 3,
  );

  const [pingTime, setPingTime] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const isEditMode = isEditModel;

  // 处理表单提交
  const handleSubmit = (values: any) => {
    // 如果是编辑模式，且API密钥没有改变（还是6个*），则设置为-1
    if (isEditMode && values.apiKey === '******') {
      values.apiKey = '-1';
    }
    if (!isEditMode) {
      values = { ...values, pingTime, status: testStatus };
    }
    onSubmit({ ...values, status: testStatus, pingTime });
  };

  const handleFormChange = () => {
    if (isEditMode) {
      setTestStatus(3);
    }
  };

  const handleTest = async () => {
    try {
      setLoading(true);
      setTestStatus(3); // 设置为测试中状态
      const values = await form.validateFields();
      if (isEditMode && values.apiKey === '******') {
        values.apiKey = '-1';
        values.modelConfigId = currentModel?.modelConfigId;
      }
      const { pingRes, pingTime } = await onTest({
        ...values,
        modelConfigId: currentModel?.modelConfigId,
      });
      setPingTime(pingTime);
      setTestStatus(pingRes);
    } catch (error) {
      setTestStatus(2); // 如果验证失败，设置为测试失败
    } finally {
      setLoading(false);
    }
  };

  // 渲染测试状态
  const renderTestStatus = useCallback(
    (testStatus: number) => {
      const statusMap: { [key: number]: string } = {
        3: t('未测试'),
        1: t('测试通过'),
        2: t('测试失败'),
        0: t('测试中...'),
      };
      const colorMap: { [key: number]: string } = {
        3: '',
        1: 'text-green-500',
        2: 'text-red-500',
        0: 'text-blue-500',
      };
      return (
        <span className={colorMap[testStatus]}>{statusMap[testStatus]}</span>
      );
    },
    [modelTypes, testStatus],
  );

  // 设置初始值
  useEffect(() => {
    if (currentModel) {
      form.setFieldsValue({
        ...currentModel,
        type: currentModel.modelType,
        apiKey: '******', // 编辑模式下显示6个*
      });
    }
  }, [currentModel, form]);

  useEffect(() => {
    setTestStatus(currentModel?.status || 3);
  }, [currentModel]);

  return (
    <Modal
      title={isEditMode ? t('编辑模型') : t('添加模型')}
      open={isModalVisible}
      onCancel={onCancel}
      footer={null}
      destroyOnClose={true}
      width={600}>
      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        onValuesChange={handleFormChange}
        className='mt-4'>
        <Form.Item
          name='factoryId'
          label={t('提供商')}
          rules={[{ required: true, message: t('请选择提供商') }]}>
          <Select
            placeholder={t('请选择提供商')}
            options={providers}
            disabled={isEditMode}
          />
        </Form.Item>

        <Form.Item
          name='modelType'
          label={t('模型类型')}
          rules={[{ required: true, message: t('请选择模型类型') }]}>
          <Select
            placeholder={t('请选择模型类型')}
            options={modelTypes}
            disabled={isEditMode}
          />
        </Form.Item>

        <Form.Item
          name='modelName'
          label={t('模型名称')}
          rules={[{ required: true, message: t('请选择模型') }]}>
          <Input placeholder={t('请填写模型')} disabled={isEditMode} />
        </Form.Item>

        <Form.Item
          name='requestUrl'
          label={t('接口地址')}
          rules={[{ required: true, message: t('请输入接口地址') }]}>
          <Input placeholder={t('请输入接口地址')} />
        </Form.Item>

        <Form.Item name='apiKey' label={t('API密钥')}>
          <Input.Password
            placeholder={
              isEditMode ? t('如需修改请输入新密钥') : t('请输入API密钥')
            }
            visibilityToggle={false}
          />
        </Form.Item>

        <Form.Item name='desc' label={t('描述')}>
          <Input.TextArea placeholder={t('请输入模型描述')} />
        </Form.Item>

        <Form.Item name='isPrivate' label={t('私有化')} valuePropName='checked'>
          <Switch />
        </Form.Item>
        <div className='flex justify-between'>
          <div className='flex items-center gap-2'>
            <Button
              onClick={handleTest}
              loading={loading}
              disabled={testStatus === 0}>
              {t('测试连接')}
            </Button>
            {renderTestStatus(testStatus)}
          </div>
          <div className='flex gap-2'>
            <Button type='primary' htmlType='submit'>
              {t('确定')}
            </Button>
            <Button onClick={onCancel}>{t('取消')}</Button>
          </div>
        </div>
      </Form>
    </Modal>
  );
};

export default ConfigModel;
