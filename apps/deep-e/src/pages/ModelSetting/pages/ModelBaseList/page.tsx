import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  App,
  Card,
  Row,
  Col,
  Select,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  addBaseModel,
  getBaseModelConfig,
  BaseModelSchema,
} from '../../api/ModelBaseList';
import api from '@/api';
import { RightOutlined, DownOutlined } from '@ant-design/icons';
import { PlusOutlined } from '@ant-design/icons';
import { z } from 'zod';
import { ExpandContent } from './components';
import { Badge } from '@/components/ui/badge';
import usePageData from '@/hooks/usePageData';
import { removeUndefinedValues } from '@/utils';

export type ModelData = Omit<z.infer<typeof BaseModelSchema>, 'baseInfo'> & {
  technicName: string;
  modelName: string;
  modelPath: string;
  modelId: number;
  template: string;
};
const ModelBaseList = () => {
  const message = App.useApp().message;
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  const [isOpen, setIsOpen] = useState(false);
  const [tableData, setTableData] = useState<ModelData[]>([]);
  const { pageData, setPageData, pagination } = usePageData();
  const [isEdit, setIsEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [modelCategories, setModelCategories] = useState<
    { label: string; value: string }[]
  >([]);

  const getBaseModelFetch = async () => {
    setLoading(true);
    try {
      const searchValues = removeUndefinedValues(searchForm.getFieldsValue());
      const res = await getBaseModelConfig({
        ...pageData,
        ...searchValues,
      });
      if (res.code) {
        throw new Error(res.msg);
      }
      const list = res.data.list.map((item) => {
        return {
          ...item,
          technicName: item.baseInfo.technicName,
          modelName: item.baseInfo.modelName,
          modelPath: item.baseInfo.modelPath,
          modelId: item.baseInfo.modelId,
          template: item.baseInfo.template,
        };
      });
      setTableData(list);
      setPageData((prev) => ({
        ...prev,
        total: res.data.total,
      }));
    } catch (error) {
      message.error(t('获取模型列表失败'));
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values: any) => {
    try {
      const res = await addBaseModel(values);
      if (!res.code) {
        setIsOpen(false);
        getBaseModelFetch();
        message.success(res.msg);
      } else {
        message.error(res?.msg || '');
      }
    } catch (error) {
      console.error('Failed to fetch model list:', error);
    }
  };

  const columns: ColumnsType<ModelData> = [
    {
      width: 200,
      title: t('模型别名'),
      dataIndex: 'technicName',
      key: 'technicName',
    },
    {
      title: t('模型名称'),
      dataIndex: 'modelName',
      key: 'modelName',
    },
    {
      title: t('模型路径'),
      dataIndex: 'modelPath',
      key: 'modelPath',
    },
    {
      title: t('架构'),
      dataIndex: 'architectures',
      key: 'architectures',
    },
    {
      title: t('标签'),
      dataIndex: 'tags',
      key: 'tags',
      render: (tags) => {
        return (
          tags &&
          tags.map((tag: string) => {
            return (
              <Badge key={tag} className='mx-1'>
                {tag}
              </Badge>
            );
          })
        );
      },
    },
    {
      title: t('训练框架'),
      dataIndex: 'trainingFramework',
      key: 'trainingFramework',
    },
    {
      title: t('磁盘占用空间'),
      dataIndex: 'diskSizeGB',
      key: 'diskSizeGB',
      render: (text) => {
        return <span>{text}</span>;
      },
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <div className='flex space-x-2'>
          <Button
            type='link'
            onClick={() => {
              setIsEdit(true);
              setIsOpen(true);
              form.setFieldsValue({
                ...record,
              });
            }}>
            {t('编辑')}
          </Button>
        </div>
      ),
    },
  ];

  // 搜索处理函数
  const onSearchFinish = () => {
    setPageData({ ...pageData, page: 1 });
    getBaseModelFetch();
  };

  // 重置搜索
  const onSearchReset = () => {
    searchForm.resetFields();
    setPageData({ ...pageData, page: 1 });
    getBaseModelFetch();
  };

  useEffect(() => {
    getBaseModelFetch();
  }, [pageData.page, pageData.pageSize]);

  useEffect(() => {
    const fetchModelCategories = async () => {
      try {
        const res = await api.getModelTypeListForChat();
        if (res.code) {
          throw new Error(res.msg);
        }
        const categories = res.data.list.map((item: any) => ({
          label: item,
          value: item,
        }));
        setModelCategories(categories);
      } catch (error) {
        console.error('获取模型类别失败:', error);
        message.error(t('获取模型类别失败'));
      }
    };
    fetchModelCategories();
  }, [message]);

  return (
    <div className='p-4 w-full min-h-full space-y-2'>
      {/* 搜索区域 */}
      <Card>
        <Form layout='inline' form={searchForm} onFinish={onSearchFinish}>
          <Row gutter={[16, 8]}>
            <Col span={6}>
              <Form.Item label={t('模型名称')} name='modelName'>
                <Input placeholder={t('请输入模型名称')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label={t('模型别名')} name='technicName'>
                <Input placeholder={t('请输入模型别名')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label={t('模型类型')} name='modelType'>
                <Input placeholder={t('请输入模型类型')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label={t('训练框架')} name='trainingFramework'>
                <Input placeholder={t('请输入训练框架')} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <div className='flex justify-start gap-2'>
                <Button type='primary' htmlType='submit'>
                  {t('查询')}
                </Button>
                <Button onClick={onSearchReset}>{t('重置')}</Button>
              </div>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 主要内容区域 */}
      <Card
        title={
          <div className='flex items-center justify-between'>
            <div>{t('基础模型管理')}</div>
            <div className='flex justify-between items-center'>
              <Button
                icon={<PlusOutlined />}
                type='primary'
                onClick={() => {
                  setIsEdit(false);
                  form.resetFields();
                  setIsOpen(true);
                }}>
                {t('添加模型')}
              </Button>
            </div>
          </div>
        }
        className='w-full h-full'>
        <Table<ModelData>
          columns={columns}
          dataSource={tableData}
          loading={loading}
          expandable={{
            expandedRowRender: (record) => <ExpandContent record={record} />,
            expandIcon: ({ expanded, onExpand, record }) => {
              return expanded ? (
                <DownOutlined onClick={(e) => onExpand(record, e)} />
              ) : (
                <RightOutlined onClick={(e) => onExpand(record, e)} />
              );
            },
          }}
          rowKey={(record) => {
            return record.modelId;
          }}
          pagination={pagination}
          scroll={{ x: true }}
        />
      </Card>
      <Modal
        title={isEdit ? t('编辑模型') : t('添加模型')}
        open={isOpen}
        destroyOnHidden
        onCancel={() => {
          setIsOpen(false);
        }}
        footer={null}>
        <Form labelCol={{ span: 6 }} onFinish={onFinish} form={form}>
          <Form.Item
            label={t('模型别名')}
            name='technicName'
            rules={[{ required: true, message: t('请输入模型别名') }]}>
            <Input disabled={isEdit} placeholder={t('请输入模型别名')} />
          </Form.Item>
          <Form.Item
            label={t('基础模型名称')}
            name='modelName'
            rules={[{ required: true, message: t('请输入基础模型名称') }]}>
            <Input disabled={isEdit} placeholder={t('请输入基础模型名称')} />
          </Form.Item>
          <Form.Item
            label={t('模型路径')}
            name='modelPath'
            rules={[{ required: true, message: t('请输入模型路径') }]}>
            <Input disabled={isEdit} placeholder={t('请输入模型路径')} />
          </Form.Item>
          <Form.Item
            label={t('模型类别')}
            name='modelCategory'
            rules={[{ required: true, message: t('请选择模型类别') }]}>
            <Select
              placeholder={t('请选择模型类别')}
              options={modelCategories}
            />
          </Form.Item>
          <Form.Item label={t('模版')} name='template'>
            <Input.TextArea rows={6} placeholder={t('请输入模型模板')} />
          </Form.Item>
          <div className='flex justify-end space-x-2'>
            <Button type='primary' htmlType='submit'>
              {t('确定')}
            </Button>
            <Button onClick={() => setIsOpen(false)}>{t('取消')}</Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};
export default ModelBaseList;
