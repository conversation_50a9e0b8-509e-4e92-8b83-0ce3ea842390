import React, { useEffect, useState } from 'react';
import { Table, Button, Card, Form, Input, Select, App } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useNavigate } from 'react-router-dom';
import { getDatasetList, deleteDataset, getStatusForDatasets } from './api';
import { DeleteButton } from '@/components/ui/delete-button';
import type { Dataset } from './api/model';
import dayjs from 'dayjs';
import useDayFormat from '@/hooks/useDayFormat';
import usePageData from '@/hooks/usePageData';
import { removeUndefinedValues } from '@/utils';
import { useActivate } from 'react-activation';

export default function Datasets() {
  const format = useDayFormat();
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [statusList, setStatusList] = useState<any[]>([]);
  const [data, setData] = useState<Dataset[]>([]);
  const { pageData, setPageData, pagination } = usePageData();

  const fetchData = async () => {
    setLoading(true);
    const values = removeUndefinedValues(form.getFieldsValue());
    try {
      const res = await getDatasetList({ ...pageData, ...values });
      if (res.code === 0) {
        setData(res.data.list);
        setPageData({
          ...pageData,
          total: res.data.total || 0,
        });
      }
    } catch (error) {
      console.error(t('获取数据集列表失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [pageData.page, pageData.pageSize]);

  useEffect(() => {
    getStatusListFetch();
  }, []);

  useActivate(() => {
    fetchData();
  });

  const handleDelete = async (record: Dataset) => {
    try {
      const res = await deleteDataset({ datasetUuid: record.datasetUuid });
      if (res.code === 0) {
        message.success(t('删除数据集成功'));
        // 重新加载数据
        fetchData();
      } else {
        message.error(t(`删除数据集失败:${res.msg}`));
      }
    } catch (error) {
      console.error(t('删除数据集失败：'), error);
    }
  };

  const getStatusListFetch = async () => {
    try {
      const res = await getStatusForDatasets();
      if (!res.code) {
        setStatusList(res.data.list);
      } else {
        message.error(res.msg || t('获取列表失败'));
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleViewDetail = (record: Dataset, type: 'view' | 'log') => {
    navigate(
      `/datasets/details?datasetUuid=${record.datasetUuid}&datasetType=${record.datasetType}&type=${type}`,
    );
  };

  const onFinish = () => {
    fetchData();
  };

  const columns: ColumnsType<Dataset> = [
    {
      title: t('名称'),
      dataIndex: 'datasetName',
      key: 'datasetName',
    },
    {
      title: t('描述'),
      dataIndex: 'datasetDesc',
      key: 'datasetDesc',
    },
    {
      title: t('状态'),
      dataIndex: 'datasetState',
      key: 'datasetState',
      render: (state: 1 | 2, record: Dataset) => {
        const colorVariants = {
          1: 'tag-success',
          2: 'tag-danger',
        };
        return (
          <div className={`${colorVariants[state]}`}>
            {record.datasetStateName}
          </div>
        );
      },
    },
    {
      title: t('创建时间'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time) => dayjs.unix(time).format(format),
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, record) => (
        <span className='flex gap-2'>
          <Button
            type='link'
            onClick={() => handleViewDetail(record, 'view')}
            className='cursor-pointer'>
            {t('查看')}
          </Button>
          <DeleteButton
            confirmTitle={t('是否删除该数据集？')}
            confirmDescription={t('该操作将永久删除该数据集，请谨慎操作。')}
            onDelete={() => handleDelete(record)}
          />
          <Button type='link' onClick={() => handleViewDetail(record, 'log')}>
            {t('日志')}
          </Button>
        </span>
      ),
    },
  ];

  useEffect(() => {
    fetchData();
  }, [pageData.page, pageData.pageSize]);

  return (
    <div className='p-4 space-y-2'>
      <header>
        <Card>
          <Form layout='inline' form={form} onFinish={onFinish}>
            <Form.Item label={t('名称')} name='datasetName'>
              <Input placeholder={t('请输入数据集名称')} />
            </Form.Item>
            <Form.Item label={t('状态')} name='datasetState'>
              <Select
                style={{
                  width: 200,
                }}
                fieldNames={{
                  label: 'stateName',
                  value: 'state',
                }}
                mode='multiple'
                options={statusList}
              />
            </Form.Item>
            <div className='flex gap-2'>
              <Button type='primary' htmlType='submit'>
                {t('查询')}
              </Button>
              <Button htmlType='reset'>{t('重置')}</Button>
            </div>
          </Form>
        </Card>
      </header>
      <Card
        title={
          <div className='flex justify-between items-center'>
            <span>{t('数据集列表')}</span>
            <Button
              type='primary'
              onClick={() => {
                navigate('/datasets/upload');
                // navigate('/runningPage', {
                //   state: {
                //     type: '2',
                //   },
                // });
              }}>
              + {t('上传数据集')}
            </Button>
          </div>
        }>
        <Table
          rowKey='datasetUuid'
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={pagination}
        />
      </Card>
    </div>
  );
}
