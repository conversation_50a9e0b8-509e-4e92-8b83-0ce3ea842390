import { Modal, Form, Input, Button, message } from 'antd';
// import ajax from '@/api';
import { memo } from 'react';

type UserSettingModalProps = {
  visible: boolean;
  close: () => void;
};

type ChangePasswordParams = {
  password: string;
  newPassword: string;
  newPasswordAgain: string;
};

const UserSettingModal: React.FC<UserSettingModalProps> = ({
  visible,
  close,
}) => {
  const changePassword = async (params: ChangePasswordParams) => {
    console.log(params);
    // const { password, newPassword } = params;
    // const res = await ajax.changePassword({
    //   password,
    //   newPassword,
    // });
    const res = { code: 0 };
    if (res.code === 0) {
      message.success(t('修改成功'));
      close();
    }
  };

  const onFinish = (values: ChangePasswordParams) => {
    if (values.newPassword !== values.newPasswordAgain) {
      return;
    }
    changePassword(values);
  };

  return (
    <Modal title={t('修改密码')} open={visible} onCancel={close} footer={false}>
      <Form onFinish={onFinish} labelCol={{ span: 8 }}>
        <Form.Item
          label={t('请输入密码')}
          name='password'
          rules={[{ required: true }]}>
          <Input placeholder={t('请输入当前密码')}></Input>
        </Form.Item>
        <Form.Item
          label={t('请输入新密码')}
          name='newPassword'
          rules={[{ required: true }]}>
          <Input type='password' placeholder={t('请输入新密码')}></Input>
        </Form.Item>
        <Form.Item
          label={t('请再次输入新密码')}
          name='newPasswordAgain'
          rules={[
            { required: true },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error('The new password that you entered do not match!'),
                );
              },
            }),
          ]}>
          <Input type='password' placeholder={t('请再次输入新密码')}></Input>
        </Form.Item>
        <Form.Item>
          <Button type='primary' htmlType='submit'>
            {t('提交')}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

const MemoUserSettingModal = memo(UserSettingModal);
export default MemoUserSettingModal;
