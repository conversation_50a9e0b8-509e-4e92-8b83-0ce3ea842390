import { Card, Table, Input, Button, Space, Modal, Form, App } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getKnowledgeList, deleteKnowledgeBase } from './api';
import { DeleteButton } from '@/components/ui/delete-button';
import { KnowledgeBase } from './types';
import { useActivate } from 'react-activation';
import dayjs from 'dayjs';
import useDayFormat from '@/hooks/useDayFormat';
import usePageData from '@/hooks/usePageData';

export default function KnowledgeHub() {
  const { message } = App.useApp();
  const format = useDayFormat();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<KnowledgeBase[]>([]);
  const { pageData, setPageData, pagination } = usePageData();

  const fetchList = async (values?: any) => {
    setLoading(true);
    try {
      const res = await getKnowledgeList({
        page: pageData.page,
        pageSize: pageData.pageSize,
        ...values,
      });
      if (res.code === 0) {
        setDataSource(res.data.list);
        setPageData((prev) => ({
          ...prev,
          total: res.data.total || 0,
        }));
      } else {
        message.error(res.msg || t('获取列表失败'));
      }
    } catch (error) {
      message.error(t('获取列表失败'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchList();
  }, [pagination.current, pagination.pageSize]);
  const handleDelete = (record: KnowledgeBase) => {
    Modal.confirm({
      title: t(`确定要删除知识库「${record.baseName}」吗？`),
      content: t('删除后不可恢复，请谨慎操作。'),
      okText: t('删除'),
      okType: 'danger',
      cancelText: t('取消'),
      onOk: async () => {
        try {
          const res = await deleteKnowledgeBase({ baseUuid: record.baseUuid });
          if (res.code === 0) {
            message.success(t('删除成功'));
            fetchList();
          } else {
            message.error(res.msg || t('删除失败'));
          }
        } catch (e) {
          message.error(t('删除失败'));
        }
      },
    });
  };

  const onFinish = (values: any) => {
    fetchList(values);
  };

  const columns = [
    {
      title: t('知识库名称'),
      dataIndex: 'baseName',
      key: 'baseName',
    },
    {
      title: t('知识库描述'),
      dataIndex: 'baseDesc',
      key: 'baseDesc',
    },
    {
      title: t('嵌入模型'),
      dataIndex: 'modelName',
      key: 'modelName',
    },
    {
      title: t('文件数目'),
      dataIndex: 'fileNum',
      key: 'fileNum',
      width: 150,
    },
    // {
    //   title: t('向量模型'),
    //   dataIndex: 'embeddingModel',
    //   key: 'embeddingModel',
    //   width: 150,
    // },
    {
      title: t('更新时间'),
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (time: number) => dayjs(time * 1000).format(format),
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_: any, record: KnowledgeBase) => (
        <div className='flex flex-col items-center'>
          <Button
            type='link'
            onClick={() =>
              navigate('/knowledgeHub/details', {
                state: { id: record.baseUuid, name: record.baseName },
              })
            }>
            {t('编辑')}
          </Button>
          {/* <Button>{t('编辑')}</Button> */}
          <DeleteButton
            className='text-[14px]'
            confirmTitle={t('是否删除该知识库？')}
            confirmDescription={t('该操作将永久删除该知识库，请谨慎操作。')}
            onDelete={() => handleDelete(record)}
          />
        </div>
      ),
    },
  ];

  const extraContent = (
    <Space>
      <Button
        type='primary'
        icon={<PlusOutlined />}
        onClick={() => navigate('/knowledgeHub/create')}>
        {t('创建知识库')}
      </Button>
    </Space>
  );

  useActivate(() => {
    fetchList();
  });

  useEffect(() => {
    fetchList();
  }, [pageData.page, pageData.pageSize]);

  return (
    <div className='p-4 space-y-2'>
      <header>
        <Card>
          <Form layout='inline' onFinish={onFinish}>
            <Form.Item label={t('名称')} name='baseName'>
              <Input placeholder={t('请输入知识库名称')} />
            </Form.Item>

            <div className='flex gap-2'>
              <Button type='primary' htmlType='submit'>
                {t('查询')}
              </Button>
              <Button htmlType='reset'>{t('重置')}</Button>
            </div>
          </Form>
        </Card>
      </header>
      <Card title={t('知识库管理')} extra={extraContent} className='w-full'>
        <Table
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          rowKey='baseUuid'
          pagination={pagination}
          className='w-full'
        />
      </Card>
    </div>
  );
}
