import { Form, Select, Input, Button, Space, Modal, Divider } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useState, useRef } from 'react';
import { SearchType } from './configTableType';
import type { InputRef } from 'antd';
type filterProps = {
  id: number;
  label: string;
  condition: string;
  value: string;
};

interface saveConfigParams {
  title: string;
  items: filterProps[];
}

const HeaderSearch: React.FC<SearchType> = ({ search }) => {
  const [form] = Form.useForm();

  const [filterGroup, setFilterGroup] = useState<filterProps[]>([
    {
      id: 1,
      label: '应用名称',
      condition: 'like',
      value: '',
    },
  ]);
  const [items, setItems] = useState<saveConfigParams[]>([]);
  const [currentSelectConfig, setCurrentSelectConfig] = useState<string | null>(
    null,
  );
  const [name, setName] = useState('');
  const inputRef = useRef<InputRef>(null);

  const onNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const addSelectItem = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault();
    setItems([...items, { title: name || 'item', items: [] }]);
    setName('');
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const [addModal, setAddModal] = useState<boolean>(false);

  const closeModal = () => {
    setAddModal(false);
  };
  const openModal = () => {
    setAddModal(true);
  };
  const saveConfig = () => {
    // const values = form.getFieldsValue();
    const configItem = items.find((item) => item.title === currentSelectConfig);
    if (configItem) {
      configItem.items = filterGroup;
    }
    setItems([...items]);
    closeModal();
  };

  const changeConfig = (value: string) => {
    setCurrentSelectConfig(value);
    const configItem = items.find((item) => item.title === value);
    if (configItem) {
      setFilterGroup(configItem.items);
    }
  };

  const addConfigItem = () => {
    setFilterGroup((prev) => {
      return [
        ...prev,
        {
          id: Math.random(),
          label: '应用名称',
          condition: 'like',
          value: '',
        },
      ];
    });
  };
  const removeItem = (id: number) => {
    setFilterGroup((prev) => {
      return prev.filter((i) => i.id !== id);
    });
  };
  const resetConifg = () => {
    changeConfig(currentSelectConfig as string);
  };
  return (
    <div className=' bg-white px-4 pt-4 mb-4 rounded-lg'>
      <div className='mb-6 '>
        <label htmlFor=''>我的配置：</label>
        <Select
          onChange={changeConfig}
          value={currentSelectConfig}
          style={{ width: '150px' }}
          options={items.map((item) => ({
            label: item.title,
            value: item.title,
          }))}
        />
      </div>
      <Form onFinish={search} form={form}>
        <Form.Item label='过滤条件'>
          <div className='flex flex-wrap'>
            {filterGroup.map((item: filterProps) => {
              return (
                <Space.Compact key={item.id} className='mx-4'>
                  <Form.Item
                    name={[item.id, 'label']}
                    initialValue={item.label}>
                    <Select
                      style={{ width: '150px' }}
                      placeholder='请选择过滤条件'
                    />
                  </Form.Item>
                  <Form.Item
                    name={[item.id, 'condition']}
                    initialValue={item.condition}>
                    <Select style={{ width: '150px' }} />
                  </Form.Item>
                  <Form.Item
                    name={[item.id, 'value']}
                    initialValue={item.value}>
                    <Input
                      style={{ width: '150px' }}
                      placeholder={t('请输入过滤值')}
                    />
                  </Form.Item>
                  <Form.Item>
                    <Button
                      disabled={filterGroup.length === 1}
                      onClick={() => removeItem(item.id)}>
                      删除
                    </Button>
                  </Form.Item>
                </Space.Compact>
              );
            })}
            <Form.Item>
              <Button onClick={addConfigItem} className='ml-4'>
                添加
              </Button>
              <Button onClick={openModal}>保存</Button>
              <Button htmlType='submit'>搜索</Button>
              <Button onClick={resetConifg}>重置</Button>
            </Form.Item>
          </div>
        </Form.Item>
      </Form>
      <Modal
        title='请选择加入到的配置'
        open={addModal}
        onCancel={closeModal}
        onOk={saveConfig}>
        <Select
          onChange={(value) => setCurrentSelectConfig(value)}
          style={{ width: '250px' }}
          dropdownRender={(menu) => {
            return (
              <>
                {menu}
                <Divider style={{ margin: '8px 0' }} />
                <Space style={{ padding: '0 8px 4px' }}>
                  <Input
                    placeholder='Please enter item'
                    ref={inputRef}
                    value={name}
                    onChange={onNameChange}
                    onKeyDown={(e) => e.stopPropagation()}
                  />
                  <Button
                    type='text'
                    icon={<PlusOutlined />}
                    onClick={addSelectItem}>
                    Add item
                  </Button>
                </Space>
              </>
            );
          }}
          value={currentSelectConfig}
          options={items.map((item) => ({
            label: item.title,
            value: item.title,
          }))}
        />
      </Modal>
    </div>
  );
};
export default HeaderSearch;
