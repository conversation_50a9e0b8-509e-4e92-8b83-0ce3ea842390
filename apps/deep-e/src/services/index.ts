// 安装axios: npm install axios
import { processError } from '@/components/ErrorBoundary/ErrorProcess';
import { message } from 'antd';
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import { getToken, setToken, getLanguage } from '@/store/features/auth';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';

NProgress.configure({
  showSpinner: false,
  trickleSpeed: 200,
  easing: 'ease',
  speed: 500,
});

export interface returnData<T> extends AxiosResponse {
  code: number;
  data: {
    list: T;
    page?: number;
    pageSize?: number;
    total?: number;
    [key: string]: any;
  };
  msg?: string;
}

// 创建 Axios 实例
const api: AxiosInstance = axios.create({
  // timeout: 10000, // 设置超时时间
});
// 将对象参数转换为 JSON 格式并拼接到 URL 后面的函数
function toQueryString(params: any) {
  const queryParts = [];
  for (const key in params) {
    if (Array.isArray(params[key])) {
      params[key].forEach((value: any) => {
        // 对于数组中的字符串值，直接使用值而不进行JSON序列化
        const encodedValue = typeof value === 'string' 
          ? encodeURIComponent(value) 
          : encodeURIComponent(JSON.stringify(value));
        queryParts.push(`${encodeURIComponent(key)}=${encodedValue}`);
      });
    } else if (typeof params[key] === 'object') {
      queryParts.push(
        `${encodeURIComponent(key)}=${encodeURIComponent(JSON.stringify(params[key]))}`,
      );
    } else {
      queryParts.push(
        `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`,
      );
    }
  }
  return queryParts.join('&');
}
// 请求拦截器
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在请求发送之前做些什么
    // 例如，添加请求头信息
    NProgress.start();
    const token = getToken();
    config.headers['X-Token'] = token || '';
    config.headers['Accept-Language'] = getLanguage() || 'zh';
    if (config.method === 'get' && config.params) {
      const queryString = toQueryString(config.params);
      config.url = `${config.url}?${queryString}`;
      delete config.params; // 删除原始的 params
    }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  },
); // 显式指定返回类型

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<returnData<any>, any>) => {
    NProgress.done();
    const { data } = response;
    //如果接口跑通了，但是返回来的code码不为0，则报一个警报
    if (data.code !== 0 && data.code !== 200 && data.code !== 20620) {
      message.error(data.msg);
      processError(data.code);
    }
    if (response.headers['new-token'] !== undefined) {
      setToken(response.headers['new-token']);
    }
    if (data.data.list === null) {
      data.data.list = [];
    }
    return data;
  },
  (error) => {
    // 对响应错误做点什么
    message.error(onErrorReason(error.message));
    return { code: 1, message: onErrorReason(error.message) };
  },
);

/** 解析http层面请求异常原因 */
function onErrorReason(message: string): string {
  if (message.includes('Network Error')) {
    return '网络异常，请检查网络情况!';
  }
  if (message.includes('timeout')) {
    return '请求超时，请重试!';
  }
  return '服务异常,请重试!,请联系管理员';
}

// 封装常用方法
const request = {
  get: (url: string, config?: AxiosRequestConfig, isMock?: boolean) => {
    if (isMock) {
      return api.get('/mock' + url, config) as Promise<returnData<any>>;
    }
    return api.get(url, config) as Promise<returnData<any>>;
  },

  post: (url: string, data?: any, config?: AxiosRequestConfig) =>
    api.post(url, data, config) as Promise<returnData<any>>,

  put: (url: string, data?: any, config?: AxiosRequestConfig) =>
    api.put(url, data, config) as Promise<returnData<any>>,
  delete: (url: string, config?: AxiosRequestConfig) =>
    api.delete(url, config) as Promise<returnData<any>>,
};

export default request;
